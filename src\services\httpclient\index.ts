import axios, { AxiosInstance, AxiosResponse, AxiosError, AxiosRequestConfig } from "axios";
import toast from "react-hot-toast";
const baseUrl = import.meta.env.VITE_APP_SERVER_URL;

export const httpClient: AxiosInstance = axios.create({
  baseURL: baseUrl,
  withCredentials: true,
});

// Enhanced HTTP client with AbortController support
export interface CancellableRequestConfig extends AxiosRequestConfig {
  signal?: AbortSignal;
}

// Utility class for managing request cancellation
export class RequestCancellation {
  private static readonly activeRequests = new Map<string, AbortController>();

  /**
   * Creates a new AbortController and registers it with a unique key
   * @param key - Unique identifier for the request
   * @returns AbortController instance
   */
  static createController(key: string): AbortController {
    // Cancel any existing request with the same key
    this.cancel(key);

    const controller = new AbortController();
    this.activeRequests.set(key, controller);

    return controller;
  }

  /**
   * Cancels a specific request by key
   * @param key - Unique identifier for the request to cancel
   */
  static cancel(key: string): void {
    const controller = this.activeRequests.get(key);
    if (controller) {
      controller.abort();
      this.activeRequests.delete(key);
    }
  }

  /**
   * Cancels all active requests
   */
  static cancelAll(): void {
    this.activeRequests.forEach((controller) => {
      controller.abort();
    });
    this.activeRequests.clear();
  }

  /**
   * Removes a controller from tracking (call this when request completes)
   * @param key - Unique identifier for the request
   */
  static cleanup(key: string): void {
    this.activeRequests.delete(key);
  }

  /**
   * Gets the current number of active requests
   */
  static getActiveRequestCount(): number {
    return this.activeRequests.size;
  }
}

type ServerErrorData = { message: string };

const errorMessageByCode: Record<number, string> = {
  400: "400 Validasiya xətası",
  404: "404 Tapılmayib!",
  500: "500 Server xətası",
};

const setupInterceptors = () => {
  httpClient.interceptors.request.use(
    (requestConfig) => {
      return requestConfig;
    },
    (error: AxiosError) => {
      console.error("Sorğu xətası:", error);
      return Promise.reject(error);
    },
  );

  httpClient.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      if (error.response) {
        const errorStatus = error.response.status;
        const errorData = error.response.data as ServerErrorData;

        if (errorStatus === 401 || errorStatus === 403) {
          toast.error(error.response.statusText);
          localStorage.clear();
          window.location.href = "/";
        }
        if (errorData.message) {
          toast.error(errorData.message);
        } else if (errorStatus in errorMessageByCode) {
          toast.error(errorMessageByCode[errorStatus]);
        } else {
          toast.error(`Server status kodu ${errorStatus}`);
        }
      } else if (error.request) {
        if (error.code === "ECONNABORTED") {
          toast.error("Sorğu üçün vaxt yekunlaşdı yenidən yoxlayın.");
        } else {
          toast.error("İnternet bağlantısını yoxlayın.");
        }
      } else {
        toast.error(`Gözlənilməz xəta: ${error.message}`);
      }
      return Promise.reject(error);
    },
  );
};

setupInterceptors();
